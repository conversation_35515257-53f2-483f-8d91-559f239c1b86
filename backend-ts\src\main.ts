import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { AppModule } from './app.module';
import * as path from 'path';
import * as fs from 'fs';

async function bootstrap() {
  // Create NestJS application with Fastify adapter
  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter(),
  );

  // Enable CORS
  app.enableCors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: '*',
    credentials: true,
  });

  // Check if frontend build directory exists
  const frontendPath = path.join(__dirname, '..', '..', 'frontend', 'dist');
  if (fs.existsSync(frontendPath) && fs.existsSync(path.join(frontendPath, 'index.html'))) {
    // Serve static frontend files
    app.useStaticAssets({
      root: frontendPath,
      prefix: '/app',
    });
  } else {
    console.warn(`WARN: Frontend build directory not found or incomplete at ${frontendPath}. Serving frontend will likely fail.`);
  }

  // Use environment-based port configuration to match Python backend
  // DEV: 2024, PROD: 8123 (matching frontend expectations)
  const isDev = process.env.NODE_ENV !== 'production';
  const port = isDev ? 2024 : 8123;

  // Start the server
  await app.listen(port, '0.0.0.0');
  console.log(`Application is running on: http://localhost:${port}`);
  console.log(`Environment: ${isDev ? 'development' : 'production'}`);
}

bootstrap();
