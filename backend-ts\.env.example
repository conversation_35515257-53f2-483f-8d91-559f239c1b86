# Environment Configuration
NODE_ENV=development

# API Keys - Required
GEMINI_API_KEY=your_gemini_api_key_here

# Agent Configuration - Optional (defaults provided)
QUERY_GENERATOR_MODEL=gemini-2.0-flash
REFLECTION_MODEL=gemini-2.5-flash-preview-04-17
ANSWER_MODEL=gemini-2.5-pro-preview-05-06
NUMBER_OF_INITIAL_QUERIES=3
MAX_RESEARCH_LOOPS=2

# LangSmith Configuration - Disabled by default
LANGCHAIN_TRACING_V2=false
LANGCHAIN_API_KEY=

# Server Configuration - Optional
PORT=2024
