import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { StateGraph, START, END, Send } from '@langchain/langgraph';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { GoogleGenAI } from '@google/genai';
import { AIMessage } from '@langchain/core/messages';
import { RunnableConfig } from '@langchain/core/runnables';

import { OverallStateAnnotation, OverallState, SourceInfo, ReflectionState } from './state';
import { ConfigurationService } from './configuration.service';
import { SearchQueryListSchema, ReflectionSchema } from './tools-and-schemas';
import {
  getCurrentDate,
  queryWriterInstructions,
  webSearcherInstructions,
  reflectionInstructions,
  answerInstructions
} from './prompts';
import {
  getResearchTopic,
  resolveUrls,
  getCitations,
  insertCitationMarkers
} from './utils';

/**
 * Query generation state for intermediate processing
 */
interface QueryGenerationState {
  query_list: Array<{ query: string; rationale: string }>;
}



/**
 * Web search state for individual search operations
 */
interface WebSearchState {
  search_query: string;
  id: number;
}

/**
 * LangGraph service that implements the agent workflow.
 * This is the core implementation that replaces the Python graph.py
 */
@Injectable()
export class GraphService {
  private genaiClient: GoogleGenAI;
  private graph: any;

  constructor(
    private configService: ConfigService,
    private configurationService: ConfigurationService,
  ) {
    // Disable LangSmith tracing completely
    process.env.LANGCHAIN_TRACING_V2 = 'false';
    process.env.LANGCHAIN_API_KEY = '';

    // Check for GEMINI_API_KEY
    const geminiApiKey = this.configService.get<string>('GEMINI_API_KEY');
    if (!geminiApiKey) {
      throw new Error('GEMINI_API_KEY is not set');
    }

    // Initialize Google Gen AI client
    this.genaiClient = new GoogleGenAI({ apiKey: geminiApiKey });

    // Build the graph
    this.buildGraph();
  }

  /**
   * Build the LangGraph state graph
   */
  private buildGraph() {
    const builder = new StateGraph(OverallStateAnnotation)
      .addNode('generate_query', this.generateQuery.bind(this))
      .addNode('web_research', this.webResearch.bind(this))
      .addNode('reflection', this.reflection.bind(this))
      .addNode('finalize_answer', this.finalizeAnswer.bind(this))
      .addEdge(START, 'generate_query')
      .addConditionalEdges(
        'generate_query',
        this.continueToWebResearch.bind(this),
        ['web_research']
      )
      .addEdge('web_research', 'reflection')
      .addConditionalEdges(
        'reflection',
        this.evaluateResearch.bind(this),
        ['web_research', 'finalize_answer']
      )
      .addEdge('finalize_answer', END);

    this.graph = builder.compile();
  }

  /**
   * Get the compiled graph for external use
   */
  getGraph() {
    return this.graph;
  }

  /**
   * LangGraph node that generates search queries based on the user's question.
   * Equivalent to the Python generate_query function.
   */
  private async generateQuery(
    state: OverallState,
    config?: RunnableConfig
  ): Promise<Partial<OverallState>> {
    try {
      const configurable = ConfigurationService.fromRunnableConfig(config);

      // Check for custom initial search query count
      const initialSearchQueryCount = state.initial_search_query_count ?? configurable.numberOfInitialQueries;

      // Initialize Gemini model
      const llm = new ChatGoogleGenerativeAI({
        model: configurable.queryGeneratorModel,
        temperature: 1.0,
        maxRetries: 2,
        apiKey: this.configService.get<string>('GEMINI_API_KEY'),
      });

      // Format the prompt
      const currentDate = getCurrentDate();
      const formattedPrompt = queryWriterInstructions
        .replace('{current_date}', currentDate)
        .replace('{research_topic}', getResearchTopic(state.messages))
        .replace('{number_queries}', initialSearchQueryCount.toString());

      // Generate the search queries using structured output
      const structuredLlm = llm.withStructuredOutput(SearchQueryListSchema);
      const result = await structuredLlm.invoke(formattedPrompt);

      return {
        // Store the query list for the next step
        search_query: result.query,
      };
    } catch (error) {
      console.error('Error in generateQuery:', error);
      // Fallback to default queries if generation fails
      return {
        search_query: ['General information search'],
      };
    }
  }

  /**
   * LangGraph conditional edge that sends search queries to web research nodes.
   * Equivalent to the Python continue_to_web_research function.
   */
  private continueToWebResearch(state: OverallState): Send[] {
    return state.search_query.map((searchQuery, idx) => 
      new Send('web_research', { search_query: searchQuery, id: idx })
    );
  }

  /**
   * LangGraph node that performs web research using Google Search API.
   * Equivalent to the Python web_research function.
   */
  private async webResearch(
    state: WebSearchState,
    config?: RunnableConfig
  ): Promise<Partial<OverallState>> {
    try {
      const configurable = ConfigurationService.fromRunnableConfig(config);

      // Format the prompt for comprehensive research
      const formattedPrompt = webSearcherInstructions
        .replace('{current_date}', getCurrentDate())
        .replace('{research_topic}', state.search_query);

      // Use Google Gen AI client with Google Search grounding (matching Python implementation)
      const response = await this.genaiClient.models.generateContent({
        model: configurable.queryGeneratorModel,
        contents: formattedPrompt,
        config: {
          tools: [{ google_search: {} }],
          temperature: 0,
        },
      });

      // Extract the response text (matching Python implementation)
      const responseText = response.text || '';

      // Resolve URLs to short URLs for saving tokens and time
      const groundingChunks = response.candidates?.[0]?.groundingMetadata?.groundingChunks || [];
      const resolvedUrls = resolveUrls(groundingChunks, state.id);

      // Get citations and add them to the generated text
      const citations = getCitations(response, resolvedUrls);
      const modifiedText = insertCitationMarkers(responseText, citations);

      // Extract sources from citations
      const sourcesGathered: SourceInfo[] = [];
      for (const citation of citations) {
        for (const segment of citation.segments) {
          sourcesGathered.push(segment);
        }
      }

      return {
        sources_gathered: sourcesGathered,
        search_query: [state.search_query],
        web_research_result: [modifiedText],
      };
    } catch (error) {
      console.error('Error in webResearch:', error);

      // Fallback to LangChain if Google AI SDK fails
      try {
        const configurable = ConfigurationService.fromRunnableConfig(config);
        const llm = new ChatGoogleGenerativeAI({
          model: configurable.queryGeneratorModel,
          temperature: 0,
          maxRetries: 2,
          apiKey: this.configService.get<string>('GEMINI_API_KEY'),
        });

        const formattedPrompt = webSearcherInstructions
          .replace('{current_date}', getCurrentDate())
          .replace('{research_topic}', state.search_query);

        const result = await llm.invoke(formattedPrompt);
        const responseText = result.content as string;

        // Create basic source info for fallback
        const sourcesGathered: SourceInfo[] = [
          {
            label: `Research on: ${state.search_query}`,
            short_url: `[${state.id + 1}]`,
            value: `Generated research summary for "${state.search_query}"`
          }
        ];

        return {
          sources_gathered: sourcesGathered,
          search_query: [state.search_query],
          web_research_result: [responseText],
        };
      } catch (fallbackError) {
        console.error('Fallback error in webResearch:', fallbackError);

        // Final fallback: Return basic response
        return {
          sources_gathered: [],
          search_query: [state.search_query],
          web_research_result: [`Research summary for: ${state.search_query}. Based on general knowledge, this topic requires further investigation. The system encountered an issue accessing current web information.`],
        };
      }
    }
  }

  /**
   * LangGraph node that identifies knowledge gaps and generates follow-up queries.
   * Equivalent to the Python reflection function.
   */
  private async reflection(
    state: OverallState,
    config?: RunnableConfig
  ): Promise<Partial<OverallState> & { _reflection_result: ReflectionState }> {
    const configurable = ConfigurationService.fromRunnableConfig(config);

    // Increment the research loop count
    const researchLoopCount = (state.research_loop_count ?? 0) + 1;
    const reasoningModel = state.reasoning_model ?? configurable.reflectionModel;

    // Format the prompt
    const currentDate = getCurrentDate();
    const formattedPrompt = reflectionInstructions
      .replace('{current_date}', currentDate)
      .replace('{research_topic}', getResearchTopic(state.messages))
      .replace('{summaries}', state.web_research_result.join('\n\n---\n\n'));

    // Initialize reasoning model
    const llm = new ChatGoogleGenerativeAI({
      model: reasoningModel,
      temperature: 1.0,
      maxRetries: 2,
      apiKey: this.configService.get<string>('GEMINI_API_KEY'),
    });

    const structuredLlm = llm.withStructuredOutput(ReflectionSchema);
    const result = await structuredLlm.invoke(formattedPrompt);

    // Create reflection state
    const reflectionState: ReflectionState = {
      is_sufficient: result.is_sufficient,
      knowledge_gap: result.knowledge_gap,
      follow_up_queries: result.follow_up_queries,
      research_loop_count: researchLoopCount,
      number_of_ran_queries: state.search_query.length,
      max_research_loops: state.max_research_loops ?? configurable.maxResearchLoops,
    };

    // Return state update with reflection result stored for evaluation
    return {
      research_loop_count: researchLoopCount,
      _reflection_result: reflectionState,
    };
  }

  /**
   * LangGraph routing function that determines the next step in the research flow.
   * Equivalent to the Python evaluate_research function.
   */
  private evaluateResearch(
    state: OverallState & { _reflection_result?: ReflectionState },
    config?: RunnableConfig
  ): string | Send[] {
    const configurable = ConfigurationService.fromRunnableConfig(config);
    const maxResearchLoops = state.max_research_loops ?? configurable.maxResearchLoops;

    // Get reflection result from state
    const reflectionResult = state._reflection_result;
    if (!reflectionResult) {
      return 'finalize_answer'; // Fallback if no reflection data
    }

    if (reflectionResult.is_sufficient || reflectionResult.research_loop_count >= maxResearchLoops) {
      return 'finalize_answer';
    } else {
      return reflectionResult.follow_up_queries.map((followUpQuery: string, idx: number) =>
        new Send('web_research', {
          search_query: followUpQuery,
          id: reflectionResult.number_of_ran_queries + idx,
        })
      );
    }
  }

  /**
   * LangGraph node that finalizes the research summary.
   * Equivalent to the Python finalize_answer function.
   */
  private async finalizeAnswer(
    state: OverallState,
    config?: RunnableConfig
  ): Promise<Partial<OverallState>> {
    const configurable = ConfigurationService.fromRunnableConfig(config);
    const reasoningModel = state.reasoning_model ?? configurable.answerModel;

    // Format the prompt
    const currentDate = getCurrentDate();
    const formattedPrompt = answerInstructions
      .replace('{current_date}', currentDate)
      .replace('{research_topic}', getResearchTopic(state.messages))
      .replace('{summaries}', state.web_research_result.join('\n---\n\n'));

    // Initialize reasoning model
    const llm = new ChatGoogleGenerativeAI({
      model: reasoningModel,
      temperature: 0,
      maxRetries: 2,
      apiKey: this.configService.get<string>('GEMINI_API_KEY'),
    });

    const result = await llm.invoke(formattedPrompt);

    // Replace short URLs with original URLs and collect used sources
    let content = result.content as string;
    const uniqueSources: SourceInfo[] = [];

    for (const source of state.sources_gathered) {
      if (content.includes(source.short_url)) {
        content = content.replace(source.short_url, source.value);
        uniqueSources.push(source);
      }
    }

    return {
      messages: [new AIMessage({ content })],
      sources_gathered: uniqueSources,
    };
  }
}
