/**
 * Comprehensive Migration Verification Test
 * Tests all API endpoints and functionality to ensure 100% parity with Python backend
 */

const BASE_URL = 'http://localhost:2024';

// Test utilities
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
  console.log(`[${timestamp}] ${prefix} ${message}`);
}

async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });
    
    const data = await response.json();
    return { status: response.status, data, ok: response.ok };
  } catch (error) {
    return { status: 0, data: null, ok: false, error: error.message };
  }
}

// Test cases
const tests = {
  async testAssistantEndpoint() {
    log('Testing Assistant Endpoint...');
    const result = await makeRequest(`${BASE_URL}/assistants/agent`);
    
    if (result.ok && result.data.assistant_id === 'agent') {
      log('Assistant endpoint test passed', 'success');
      return true;
    } else {
      log(`Assistant endpoint test failed: ${JSON.stringify(result)}`, 'error');
      return false;
    }
  },

  async testAssistantGraph() {
    log('Testing Assistant Graph Endpoint...');
    const result = await makeRequest(`${BASE_URL}/assistants/agent/graph`);

    if (result.ok && result.data.nodes && result.data.edges) {
      log('Assistant graph endpoint test passed', 'success');
      return true;
    } else {
      log(`Assistant graph endpoint test failed: ${JSON.stringify(result)}`, 'error');
      return false;
    }
  },

  async testThreadCreation() {
    log('Testing Thread Creation...');
    const result = await makeRequest(`${BASE_URL}/threads`, {
      method: 'POST',
      body: JSON.stringify({}),
    });
    
    if (result.ok && result.data.thread_id) {
      log(`Thread created successfully: ${result.data.thread_id}`, 'success');
      return result.data.thread_id;
    } else {
      log(`Thread creation failed: ${JSON.stringify(result)}`, 'error');
      return null;
    }
  },

  async testLegacyAPI() {
    log('Testing Legacy API Endpoint...');
    const payload = {
      messages: [
        {
          type: 'human',
          content: 'What is artificial intelligence?'
        }
      ]
    };

    const result = await makeRequest(`${BASE_URL}/api/run`, {
      method: 'POST',
      body: JSON.stringify(payload),
    });

    if (result.ok && result.data.result && result.data.result.messages) {
      log('Legacy API test passed', 'success');
      return true;
    } else {
      log(`Legacy API test failed: ${JSON.stringify(result)}`, 'error');
      return false;
    }
  },

  async testStreamingAPI(threadId) {
    log('Testing Streaming API...');
    const payload = {
      assistant_id: 'agent',
      input: {
        messages: [
          {
            type: 'human',
            content: 'What is machine learning?'
          }
        ]
      },
      config: {
        configurable: {
          initial_search_query_count: 1,
          max_research_loops: 1
        }
      }
    };

    try {
      const response = await fetch(`${BASE_URL}/threads/${threadId}/runs/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        log(`Streaming API failed with status: ${response.status}`, 'error');
        return false;
      }

      // For Node.js, read the response as text and parse events
      const responseText = await response.text();
      const lines = responseText.split('\n');

      let hasMetadata = false;
      let hasData = false;
      let hasEnd = false;

      for (const line of lines) {
        if (line.startsWith('event: ')) {
          const eventType = line.substring(7).trim();

          if (eventType === 'metadata') hasMetadata = true;
          if (eventType === 'data') hasData = true;
          if (eventType === 'end') hasEnd = true;
        }
      }

      if (hasMetadata && hasData && hasEnd) {
        log('Streaming API test passed', 'success');
        return true;
      } else {
        log(`Streaming API test incomplete: metadata=${hasMetadata}, data=${hasData}, end=${hasEnd}`, 'error');
        return false;
      }

    } catch (error) {
      log(`Streaming API test error: ${error.message}`, 'error');
      return false;
    }
  },

  async testNonStreamingAPI(threadId) {
    log('Testing Non-Streaming API...');
    const payload = {
      assistant_id: 'agent',
      input: {
        messages: [
          {
            type: 'human',
            content: 'What is deep learning?'
          }
        ]
      },
      config: {
        configurable: {
          initial_search_query_count: 1,
          max_research_loops: 1
        }
      }
    };

    const result = await makeRequest(`${BASE_URL}/threads/${threadId}/runs`, {
      method: 'POST',
      body: JSON.stringify(payload),
    });

    if (result.ok && result.data.output && result.data.output.messages) {
      log('Non-streaming API test passed', 'success');
      return true;
    } else {
      log(`Non-streaming API test failed: ${JSON.stringify(result)}`, 'error');
      return false;
    }
  }
};

// Main test runner
async function runMigrationVerification() {
  log('🚀 Starting Migration Verification Tests');
  log('==========================================');

  const results = {};
  let threadId = null;

  // Wait for server to be ready
  log('⏳ Waiting for server to start...');
  await new Promise(resolve => setTimeout(resolve, 2000));

  try {
    // Test 1: Assistant endpoint
    results.assistant = await tests.testAssistantEndpoint();

    // Test 2: Assistant graph
    results.assistantGraph = await tests.testAssistantGraph();

    // Test 3: Thread creation
    threadId = await tests.testThreadCreation();
    results.threadCreation = threadId !== null;

    // Test 4: Legacy API
    results.legacyAPI = await tests.testLegacyAPI();

    // Test 5: Streaming API (if thread was created)
    if (threadId) {
      results.streamingAPI = await tests.testStreamingAPI(threadId);
    } else {
      results.streamingAPI = false;
      log('Skipping streaming API test - no thread ID', 'error');
    }

    // Test 6: Non-streaming API (if thread was created)
    if (threadId) {
      results.nonStreamingAPI = await tests.testNonStreamingAPI(threadId);
    } else {
      results.nonStreamingAPI = false;
      log('Skipping non-streaming API test - no thread ID', 'error');
    }

  } catch (error) {
    log(`Test execution error: ${error.message}`, 'error');
  }

  // Summary
  log('\n📊 Migration Verification Results');
  log('==================================');
  
  const testNames = {
    assistant: 'Assistant Endpoint',
    assistantGraph: 'Assistant Graph',
    threadCreation: 'Thread Creation',
    legacyAPI: 'Legacy API',
    streamingAPI: 'Streaming API',
    nonStreamingAPI: 'Non-Streaming API'
  };

  for (const [key, name] of Object.entries(testNames)) {
    const status = results[key] ? '✅ PASS' : '❌ FAIL';
    log(`${name}: ${status}`);
  }

  const totalPassed = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;

  log(`\nOverall: ${totalPassed}/${totalTests} tests passed`);

  if (totalPassed === totalTests) {
    log('🎉 All tests passed! Migration is complete and functional.', 'success');
    return true;
  } else {
    log('⚠️  Some tests failed. Migration may be incomplete.', 'error');
    return false;
  }
}

// Import fetch for Node.js
import('node-fetch').then(({ default: fetch }) => {
  global.fetch = fetch;
  runMigrationVerification().catch(console.error);
}).catch(() => {
  // Fallback for environments where node-fetch is not available
  console.log('Note: Using built-in fetch or falling back to basic testing');
  runMigrationVerification().catch(console.error);
});
