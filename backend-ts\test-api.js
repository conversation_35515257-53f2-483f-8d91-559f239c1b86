/**
 * Simple test script to verify the API is working
 */
import fetch from 'node-fetch';

async function testLegacyApi() {
  console.log('\n🔍 Testing Legacy API Endpoint...');

  try {
    const apiUrl = 'http://localhost:2024/api/run';

    const testMessage = {
      messages: [
        {
          type: 'human',
          content: 'What are the latest developments in quantum computing?'
        }
      ]
    };

    console.log('Sending request to:', apiUrl);
    console.log('Payload:', JSON.stringify(testMessage, null, 2));

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testMessage),
    });

    const responseData = await response.json();

    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(responseData, null, 2));

    if (response.status === 200 && responseData.result) {
      console.log('✅ Legacy API test passed');
      return true;
    } else {
      console.log('❌ Legacy API test failed');
      return false;
    }

  } catch (error) {
    console.error('❌ Legacy API test error:', error);
    return false;
  }
}

async function testAssistantEndpoint() {
  console.log('\n🔍 Testing Assistant Endpoint...');

  try {
    const assistantUrl = 'http://localhost:2024/assistants/agent';
    console.log('Testing assistant endpoint:', assistantUrl);

    const assistantResponse = await fetch(assistantUrl);
    const assistantData = await assistantResponse.json();

    console.log('Assistant response:', JSON.stringify(assistantData, null, 2));

    if (assistantResponse.status === 200) {
      console.log('✅ Assistant endpoint test passed');
      return true;
    } else {
      console.log('❌ Assistant endpoint test failed');
      return false;
    }

  } catch (error) {
    console.error('❌ Assistant endpoint test error:', error);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting API Tests');
  console.log('====================');

  const results = {
    legacy: false,
    assistant: false,
  };

  // Wait for server to be ready
  console.log('⏳ Waiting for server to start...');
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Run tests
  results.legacy = await testLegacyApi();
  results.assistant = await testAssistantEndpoint();

  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('========================');
  console.log(`Legacy API: ${results.legacy ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Assistant API: ${results.assistant ? '✅ PASS' : '❌ FAIL'}`);

  const totalPassed = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;

  console.log(`\nOverall: ${totalPassed}/${totalTests} tests passed`);

  if (totalPassed === totalTests) {
    console.log('🎉 All tests passed! Migration is working.');
  } else {
    console.log('⚠️  Some tests failed. Check the logs above for details.');
  }

  return totalPassed === totalTests;
}

// Run tests
runTests().catch(console.error);
