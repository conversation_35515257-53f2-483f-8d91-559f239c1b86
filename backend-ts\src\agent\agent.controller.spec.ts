import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { AgentController } from './agent.controller';
import { GraphService } from './graph.service';
import { ConfigurationService } from './configuration.service';

describe('AgentController', () => {
  let controller: AgentController;
  let graphService: GraphService;

  const mockGraphService = {
    getGraph: jest.fn().mockReturnValue({
      invoke: jest.fn().mockResolvedValue({
        messages: [{ content: 'Test response', type: 'ai' }],
        search_query: ['test query'],
        web_research_result: ['test result'],
        sources_gathered: [],
        initial_search_query_count: 3,
        max_research_loops: 2,
        research_loop_count: 1,
        reasoning_model: 'gemini-2.5-pro-preview-05-06',
      }),
    }),
  };

  const mockConfigService = {
    get: jest.fn((key: string, defaultValue?: any) => {
      const config = {
        GEMINI_API_KEY: 'test-api-key',
        NODE_ENV: 'test',
      };
      return config[key] || defaultValue;
    }),
  };

  const mockConfigurationService = {
    queryGeneratorModel: 'gemini-2.0-flash',
    reflectionModel: 'gemini-2.5-flash-preview-04-17',
    answerModel: 'gemini-2.5-pro-preview-05-06',
    numberOfInitialQueries: 3,
    maxResearchLoops: 2,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AgentController],
      providers: [
        {
          provide: GraphService,
          useValue: mockGraphService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: ConfigurationService,
          useValue: mockConfigurationService,
        },
      ],
    }).compile();

    controller = module.get<AgentController>(AgentController);
    graphService = module.get<GraphService>(GraphService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('runAgent', () => {
    it('should successfully run agent with valid input', async () => {
      const requestBody = {
        messages: [
          {
            content: 'What is the latest news about AI?',
            type: 'human' as const,
          },
        ],
      };

      const result = await controller.runAgent(requestBody);

      expect(result).toBeDefined();
      expect(result.result).toBeDefined();
      expect(result.result.messages).toHaveLength(1);
      expect(graphService.getGraph().invoke).toHaveBeenCalledWith(
        expect.objectContaining({
          messages: requestBody.messages,
          search_query: [],
          web_research_result: [],
          sources_gathered: [],
          initial_search_query_count: 3,
          max_research_loops: 2,
          research_loop_count: 0,
          reasoning_model: 'gemini-2.5-pro-preview-05-06',
        }),
        expect.objectContaining({
          configurable: {},
        }),
      );
    });

    it('should use custom configuration when provided', async () => {
      const requestBody = {
        messages: [
          {
            content: 'Test message',
            type: 'human' as const,
          },
        ],
        config: {
          configurable: {
            initial_search_query_count: 5,
            max_research_loops: 3,
            reasoning_model: 'custom-model',
          },
        },
      };

      const result = await controller.runAgent(requestBody);

      expect(result).toBeDefined();
      expect(graphService.getGraph().invoke).toHaveBeenCalledWith(
        expect.objectContaining({
          initial_search_query_count: 5,
          max_research_loops: 3,
          reasoning_model: 'custom-model',
        }),
        expect.objectContaining({
          configurable: requestBody.config.configurable,
        }),
      );
    });

    it('should handle errors gracefully', async () => {
      const requestBody = {
        messages: [
          {
            content: 'Test message',
            type: 'human' as const,
          },
        ],
      };

      // Mock graph service to throw an error
      mockGraphService.getGraph.mockReturnValueOnce({
        invoke: jest.fn().mockRejectedValue(new Error('Test error')),
      });

      await expect(controller.runAgent(requestBody)).rejects.toThrow();
    });
  });
});
