import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { HealthController } from './health.controller';

describe('HealthController', () => {
  let controller: HealthController;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn((key: string, defaultValue?: any) => {
      const config = {
        NODE_ENV: 'test',
        GEMINI_API_KEY: 'test-api-key',
        QUERY_GENERATOR_MODEL: 'gemini-2.0-flash',
        REFLECTION_MODEL: 'gemini-2.5-flash-preview-04-17',
        ANSWER_MODEL: 'gemini-2.5-pro-preview-05-06',
      };
      return config[key] || defaultValue;
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [HealthController],
      providers: [
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    controller = module.get<HealthController>(HealthController);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getHealth', () => {
    it('should return basic health status', () => {
      const result = controller.getHealth();

      expect(result).toBeDefined();
      expect(result.status).toBe('ok');
      expect(result.timestamp).toBeDefined();
      expect(result.uptime).toBeDefined();
      expect(result.environment).toBe('test');
    });
  });

  describe('getDetailedHealth', () => {
    it('should return detailed health information', () => {
      const result = controller.getDetailedHealth();

      expect(result).toBeDefined();
      expect(result.status).toBe('ok');
      expect(result.checks).toBeDefined();
      expect(result.checks.gemini_api_key).toBe('configured');
      expect(result.checks.environment_variables).toBeDefined();
      expect(result.memory).toBeDefined();
      expect(result.node_version).toBeDefined();
    });
  });

  describe('getReadiness', () => {
    it('should return ready when API key is configured', () => {
      const result = controller.getReadiness();

      expect(result).toBeDefined();
      expect(result.status).toBe('ready');
      expect(result.timestamp).toBeDefined();
    });

    it('should return not ready when API key is missing', () => {
      mockConfigService.get.mockImplementation((key: string, defaultValue?: any) => {
        if (key === 'GEMINI_API_KEY') return undefined;
        return defaultValue;
      });

      const result = controller.getReadiness();

      expect(result).toBeDefined();
      expect(result.status).toBe('not_ready');
      expect(result.reason).toBe('GEMINI_API_KEY not configured');
    });
  });

  describe('getLiveness', () => {
    it('should return alive status', () => {
      const result = controller.getLiveness();

      expect(result).toBeDefined();
      expect(result.status).toBe('alive');
      expect(result.timestamp).toBeDefined();
      expect(result.uptime).toBeDefined();
    });
  });
});
